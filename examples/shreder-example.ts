/* eslint-disable no-console, ts/prefer-nullish-coalescing */
import { shreder } from '../src'

async function main() {
    console.log('🚀 Starting Shreder client example...')

    // Create client connecting to Shreder endpoint
    const client = new shreder.ShrederClient('http://localhost:9991', {
        // token: 'your-auth-token', // Optional, will be sent with 'authorization' metadata
    })

    console.log('✅ Shreder client created')

    try {
        // Example 1: Subscribe to entries (raw block data)
        console.log('\n📦 Subscribing to entries...')

        const entriesStream = await client.subscribeEntries()
        console.log('✅ Entries stream created')

        // Process a few entries then stop
        let entryCount = 0
        const maxEntries = 5

        for await (const entry of entriesStream) {
            console.log(`📦 Entry ${entryCount + 1}:`)
            console.log(`  - Slot: ${entry.slot.toString()}`)
            console.log(`  - Data length: ${entry.entries.length} bytes`)

            entryCount++

            if (entryCount >= maxEntries) {
                console.log(`\n🛑 Stopping after ${maxEntries} entries`)
                break
            }
        }
    } catch (error) {
        console.error('❌ Error with entries stream:', error)
    }

    try {
        // Example 2: Subscribe to filtered transactions
        console.log('\n💰 Subscribing to filtered transactions...')

        const transactionsStream = await client.subscribeTransactions()
        console.log('✅ Transactions stream created')

        // Send transaction filter request
        console.log('📤 Sending filter request...')

        await transactionsStream.write(shreder.createSubscribeTransactionsRequest({
            'pumpfun': shreder.createSubscribeRequestFilterTransactions({
                accountRequired: ['6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P'], // PumpFun program ID
                accountInclude: [],
                accountExclude: [],
            }),
            'test': shreder.createSubscribeRequestFilterTransactions({
                accountInclude: ['11111111111111111111111111111112'], // System program
            }),
        }))

        console.log('✅ Filter request sent')

        // Process a few transactions then stop
        let txCount = 0
        const maxTxs = 3

        for await (const response of transactionsStream) {
            console.log(`💰 Transaction ${txCount + 1}:`)
            console.log(`  - Filters matched: [${response.filters.join(', ')}]`)
            console.log(`  - Slot: ${response.transaction?.slot.toString() || 'N/A'}`)
            console.log(`  - Created at: ${response.createdAt?.toISOString() || 'N/A'}`)

            if (response.transaction?.transaction) {
                const tx = response.transaction.transaction
                console.log(`  - Signatures: ${tx.signatures.length}`)

                if (tx.signatures.length > 0) {
                    console.log(`    - First signature: ${tx.signatures[0].toString('hex').slice(0, 16)}...`)
                }

                console.log(`  - Account keys: ${tx.message?.accountKeys.length || 0}`)
                console.log(`  - Instructions: ${tx.message?.instructions.length || 0}`)
            }

            txCount++

            if (txCount >= maxTxs) {
                console.log(`\n🛑 Stopping after ${maxTxs} transactions`)
                break
            }
        }
    } catch (error) {
        console.error('❌ Error with transactions stream:', error)
    }

    console.log('\n🎉 Shreder client example completed!')
}

// Run the example
main().catch((error) => {
    console.error('💥 Example failed:', error)
    process.exit(1)
})

/* eslint-enable no-console, ts/prefer-nullish-coalescing */
