import type { SubscribeEntriesRequest, SubscribeRequestFilterTransactions, SubscribeTransactionsRequest } from './generated/shredstream'

export const createSubscribeEntriesRequest = (request: Partial<SubscribeEntriesRequest> = {}): SubscribeEntriesRequest => ({
    ...request,
})

export const createSubscribeTransactionsRequest = (transactions: Record<string, SubscribeRequestFilterTransactions>): SubscribeTransactionsRequest => ({
    transactions,
})

export const createSubscribeRequestFilterTransactions = (filter: Partial<SubscribeRequestFilterTransactions>): SubscribeRequestFilterTransactions => ({
    accountInclude: [],
    accountExclude: [],
    accountRequired: [],
    ...filter,
})
