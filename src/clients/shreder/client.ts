import type { ClientReadableStream, Metadata } from '@grpc/grpc-js'
import { EMPTY_METADATA } from '../../constants'
import { type CreateGrpcClientOptions, type CreateStreamIteratorOptions, createDuplexStreamIterator, createGrpcClient, createReadableStream } from '../../utils'
import { type Entry, ShrederServiceClient, type SubscribeEntriesRequest } from './generated/shredstream'

export const EMPTY_ENTRIES_REQUEST: SubscribeEntriesRequest = {}

export type ShrederClientOptions = CreateGrpcClientOptions & CreateStreamIteratorOptions<Entry, Entry>

export class ShrederClient {
    protected readonly grpc: ShrederServiceClient

    public constructor(url: string, protected readonly options: ShrederClientOptions = {}) {
        this.grpc = createGrpcClient(ShrederServiceClient, url, { tokenMetadataKey: 'authorization', ...options })
    }

    public async subscribeEntries(request: SubscribeEntriesRequest = EMPTY_ENTRIES_REQUEST, metadata?: Metadata) {
        return this.createReadableStream(this.grpc.subscribeEntries.bind(this.grpc), request, metadata)
    }

    public async subscribeTransactions() {
        return createDuplexStreamIterator(() => this.grpc.subscribeTransactions(), {})
    }

    protected createReadableStream(method: (request: SubscribeEntriesRequest, metadata?: Metadata) => ClientReadableStream<Entry>, request: SubscribeEntriesRequest, metadata?: Metadata) {
        return createReadableStream<Entry>(() => method.call(this.grpc, request, metadata ?? EMPTY_METADATA), this.options)
    }
}
