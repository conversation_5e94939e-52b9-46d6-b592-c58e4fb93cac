This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
proto/
  shredstream.proto
src/
  examples/
    entries/
      main.rs
      Readme.md
    transactions/
      main.rs
      Readme.md
.gitignore
build.rs
Cargo.toml
LICENSE
Readme.md
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="proto/shredstream.proto">
syntax = "proto3";

package shredstream;
import "google/protobuf/timestamp.proto";


service ShrederService {
  rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream Entry);
  rpc SubscribeTransactions(stream SubscribeTransactionsRequest) returns (stream SubscribeTransactionsResponse);
}

message SubscribeEntriesRequest {
  // tbd: we may want to add filters here
}

message SubscribeTransactionsRequest {
  map<string, SubscribeRequestFilterTransactions> transactions = 3;
}

message SubscribeTransactionsResponse {
  repeated string filters = 1;
  SubscribeUpdateTransaction transaction = 4;
  google.protobuf.Timestamp created_at = 11;
}

message SubscribeUpdateTransaction {
  Transaction transaction = 1;
  uint64 slot = 2;
}

message SubscribeRequestFilterTransactions {
  repeated string account_include = 3;
  repeated string account_exclude = 4;
  repeated string account_required = 6;
}

message Entry {
  // the slot that the entry is from
  uint64 slot = 1;

  // Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
  bytes entries = 2;
}

message MessageHeader {
  uint32 num_required_signatures = 1;
  uint32 num_readonly_signed_accounts = 2;
  uint32 num_readonly_unsigned_accounts = 3;
}

message CompiledInstruction {
  uint32 program_id_index = 1;
  bytes accounts = 2;
  bytes data = 3;
}

message MessageAddressTableLookup {
  bytes account_key = 1;
  bytes writable_indexes = 2;
  bytes readonly_indexes = 3;
}

message Message {
  MessageHeader header = 1;
  repeated bytes account_keys = 2;
  bytes recent_blockhash = 3;
  repeated CompiledInstruction instructions = 4;
  bool versioned = 5;
  repeated MessageAddressTableLookup address_table_lookups = 6;
}

message Transaction {
  repeated bytes signatures = 1;
  Message message = 2;
}
</file>

<file path="src/examples/entries/main.rs">
pub mod shredstream {
    tonic::include_proto!("shredstream");
}

use shredstream::{shreder_service_client::ShrederServiceClient, SubscribeEntriesRequest};
use tonic::{Response, Status, Streaming};

#[tokio::main]
async fn main() -> Result<(), std::io::Error> {
    let entrypoint = "http://localhost:9991";
    let mut client = ShrederServiceClient::connect(entrypoint).await.unwrap();
    let mut stream = client
        .subscribe_entries(SubscribeEntriesRequest {})
        .await
        .unwrap()
        .into_inner();

    while let Some(slot_entry) = stream.message().await.unwrap() {
        let entries =
            match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                Ok(e) => e,
                Err(e) => {
                    println!("Deserialization failed with err: {e}");
                    continue;
                }
            };

        println!(
            "slot {}, entries: {}, transactions: {}",
            slot_entry.slot,
            entries.len(),
            entries.iter().map(|e| e.transactions.len()).sum::<usize>()
        );
    }
    Ok(())
}
</file>

<file path="src/examples/entries/Readme.md">
# Shreder ShredStream Client Implementation Guide

Shreder is the fastest way to get block updates and receive transaction data on Solana.
Basically, Shreder is a shred streaming service. We stream raw transaction data (shreds) directly to your app — no polling, no delays.

This guide describes the process of creating a client for Shreder ShredStream using Rust and gRPC. Shreder allows you to receive streaming data with Solana network transactions in real-time (0 block).

Shreder website: https://shreder.xyz/
Discord: https://discord.gg/5FPgveHn
X: https://x.com/ShrederXyz

To run this example please use the following command
```bash
cargo run --example entries
```
# Example reconstruction
## 1. Setting Up the Project and Dependencies

### a. Create a New Cargo Project

Open your terminal and run the following commands:

```bash
cargo new shredstream_client
cd shredstream_client
```

### b. Edit Cargo.toml

Open the Cargo.toml file and add the required dependencies:

```toml
[package]
name = "shredstream_client"
version = "0.1.0"
edition = "2021"

[dependencies]
tonic = "0.9"                             # Core gRPC library
tokio = { version = "1", features = ["full"] } # Asynchronous runtime
prost = "0.11"                            # Used by Tonic for (de)serialization of messages
bincode = "1.3.3"                         # For deserializing Solana data
solana-entry = "1.16"                     # For working with Solana entries

[build-dependencies]
tonic-build = "0.9"                       # Used to generate Rust code from proto files
```

## 2. Generating Rust Code from the Proto File

### a. Place the Proto File

Create a directory named `proto` in the root of your project, and add the `shredstream.proto` file:

```protobuf
syntax = "proto3";

package shredstream;

service ShrederService {
  rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream SlotEntries);
}

message SubscribeEntriesRequest {}

message SlotEntries {
  uint64 slot = 1;
  bytes entries = 2;
}
```

### b. Create the build.rs File

In the root of your project, create a file named `build.rs` with the following content to compile the proto file:

```rust
fn main() {
    tonic_build::compile_protos("proto/shredstream.proto")
        .expect("Failed to compile proto files");
}
```

When you build the project, Cargo will execute `build.rs` to generate the Rust modules from your proto definitions.

## 3. Implementing the gRPC Client

### a. Import Dependencies

Add dependencies to connect to the ShredStream server:

```rust
// Import the generated modules. The string here should match the package name in your proto.
pub mod shredstream {
    tonic::include_proto!("shredstream");
}

use shredstream::{shreder_service_client::ShrederServiceClient, SubscribeEntriesRequest};
use solana_entry::entry::Entry;
```

### b. Create gRPC Client

In the `main` function, write the following code to create a gRPC connection:

```rust
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Replace with your ShredStream server address
    let endpoint = "http://127.0.0.1:9999";

    println!("Connecting to ShredStream server at: {}", endpoint);

    let mut client = ShrederServiceClient::connect(endpoint)
        .await?;

    println!("Successfully connected to server");

    // Continue with the code...
```

### c. Subscribe to Transaction Updates

Create a stream to get transaction data:

```rust
    println!("Subscribing to entries stream");

    let mut stream = client
        .subscribe_entries(SubscribeEntriesRequest {})
        .await?
        .into_inner();

    println!("Successfully subscribed. Waiting for data...");
```

### d. Handle New Entries

With this stream, you will receive entries. Each Entry contains several transactions.
Type definitions of the entries and transactions can be found in the [documentation](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html).

```rust
    while let Some(slot_entry) = stream.message().await? {
        let entries = match bincode::deserialize::<Vec<Entry>>(&slot_entry.entries) {
            Ok(e) => e,
            Err(e) => {
                println!("Deserialization failed with error: {}", e);
                continue;
            }
        };

        println!(
            "Slot {}, entries: {}, transactions: {}",
            slot_entry.slot,
            entries.len(),
            entries.iter().map(|e| e.transactions.len()).sum::<usize>()
        );

        // Add your custom processing logic here
        process_entries(slot_entry.slot, &entries);
    }

    Ok(())
}

fn process_entries(slot: u64, entries: &[Entry]) {
    // Example processing function
    for (i, entry) in entries.iter().enumerate() {
        if !entry.transactions.is_empty() {
            println!("Entry {}: Contains {} transactions", i, entry.transactions.len());
            // Process individual transactions here
        }
    }
}
```

## 4. Complete Example

Here's a complete working example of the ShredStream client:

```rust
pub mod shredstream {
    tonic::include_proto!("shredstream");
}

use shredstream::{shreder_service_client::ShrederServiceClient, SubscribeEntriesRequest};
use solana_entry::entry::Entry;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let endpoint = "http://127.0.0.1:9999";
    println!("Connecting to ShredStream server at: {}", endpoint);

    let mut client = ShrederServiceClient::connect(endpoint)
        .await?;

    println!("Successfully connected to server");

    let mut stream = client
        .subscribe_entries(SubscribeEntriesRequest {})
        .await?
        .into_inner();

    println!("Successfully subscribed. Waiting for data...");

    while let Some(slot_entry) = stream.message().await? {
        let entries = match bincode::deserialize::<Vec<Entry>>(&slot_entry.entries) {
            Ok(e) => e,
            Err(e) => {
                println!("Deserialization failed with error: {}", e);
                continue;
            }
        };

        println!(
            "Slot {}, entries: {}, transactions: {}",
            slot_entry.slot,
            entries.len(),
            entries.iter().map(|e| e.transactions.len()).sum::<usize>()
        );

        // Process entries (optional)
        for entry in &entries {
            if !entry.transactions.is_empty() {
                // Custom transaction processing logic
            }
        }
    }

    Ok(())
}
```

## 5. Building and Running the Client

To build and run your ShredStream client:

```bash
cargo run
```

Make sure to update the endpoin in this example. You can get access by contacting Shreder team via official Discord or Website.

## 6. Further Resources

- [Solana Documentation](https://docs.solana.com/)
- [Tonic gRPC Documentation](https://github.com/hyperium/tonic)
- [Solana Entry Documentation](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html)


Shreder website: https://shreder.xyz/
Discord: https://discord.gg/5FPgveHn
X: https://x.com/ShrederXyz

Connect with us to get access to Shreder.
</file>

<file path="src/examples/transactions/main.rs">
pub mod shredstream {
    tonic::include_proto!("shredstream");
}

use futures::{channel::mpsc::unbounded, sink::SinkExt};
use shredstream::{
    shreder_service_client::ShrederServiceClient, SubscribeRequestFilterTransactions,
    SubscribeTransactionsRequest, SubscribeTransactionsResponse,
};
use tonic::{Response, Streaming};

#[tokio::main]
async fn main() -> Result<(), std::io::Error> {
    let entrypoint = "http://localhost:9991";
    let mut client = ShrederServiceClient::connect(entrypoint).await.unwrap();

    let request = SubscribeTransactionsRequest {
        transactions: maplit::hashmap! {
            "pumpfun".to_owned() => SubscribeRequestFilterTransactions {
                account_exclude: vec![],
                account_include: vec![],
                account_required: vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_owned()]
            }
        },
    };
    let (mut subscribe_tx, subscribe_rx) = unbounded();
    let response: Response<Streaming<SubscribeTransactionsResponse>> =
        client.subscribe_transactions(subscribe_rx).await.unwrap();

    let mut stream = response.into_inner();

    let _ = subscribe_tx.send(request).await;

    while let Some(message) = stream.message().await.unwrap() {
        println!(
            "Filters: {:?}, Sig: {:?}",
            message.filters,
            bs58::encode(&message.transaction.unwrap().transaction.unwrap().signatures[0])
                .into_string()
        )
    }

    Ok(())
}
</file>

<file path="src/examples/transactions/Readme.md">
# Shreder ShredStream Client Implementation Guide

Shreder is the fastest way to get block updates and receive transaction data on Solana.
Basically, Shreder is a shred streaming service. We stream raw transaction data (shreds) directly to your app — no polling, no delays.

This guide describes the process of creating a client for Shreder ShredStream using Rust and gRPC. Shreder allows you to receive streaming data with Solana network transactions in real-time (0 block).

Shreder website: https://shreder.xyz/
Discord: https://discord.gg/5FPgveHn
X: https://x.com/ShrederXyz

To run this example please use the following command
```bash
cargo run --example transactions
```
# Example reconstruction
## 1. Setting Up the Project and Dependencies

### a. Create a New Cargo Project

Open your terminal and run the following commands:

```bash
cargo new shreder_client
cd shreder_client
```

### b. Edit Cargo.toml

Open the Cargo.toml file and add the required dependencies:

```toml
[package]
name = "shreder_client"
version = "0.1.0"
edition = "2021"

[dependencies]
tonic = "0.9"                             # Core gRPC library
tokio = { version = "1", features = ["full"] } # Asynchronous runtime
prost = "0.11"                            # Used by Tonic for (de)serialization of messages
bincode = "1.3.3"                         # For deserializing Solana data
solana-entry = "1.16"                     # For working with Solana entries
futures = "0.3.24"
maplit = "1.0.2"
prost-types = "0.12"
bs58 = "0.4"

[build-dependencies]
tonic-build = "0.9"                       # Used to generate Rust code from proto files
protobuf-src = "1"
```

## 2. Generating Rust Code from the Proto File

### a. Place the Proto File

Create a directory named `proto` in the root of your project, and add the `shredstream.proto` file:

```protobuf
syntax = "proto3";

package shredstream;
import "google/protobuf/timestamp.proto";



service ShrederService {
  rpc SubscribeEntries(SubscribeEntriesRequest) returns (stream Entry);
  rpc SubscribeTransactions(stream SubscribeTransactionsRequest) returns (stream SubscribeTransactionsResponse);
}

message SubscribeEntriesRequest {
  // tbd: we may want to add filters here
}

message SubscribeTransactionsRequest {
  map<string, SubscribeRequestFilterTransactions> transactions = 3;
}

message SubscribeTransactionsResponse {
  repeated string filters = 1;
  SubscribeUpdateTransaction transaction = 4;
  google.protobuf.Timestamp created_at = 11;
}

message SubscribeUpdateTransaction {
  Transaction transaction = 1;
  uint64 slot = 2;
}

message SubscribeRequestFilterTransactions {
  repeated string account_include = 3;
  repeated string account_exclude = 4;
  repeated string account_required = 6;
}

message Entry {
  // the slot that the entry is from
  uint64 slot = 1;

  // Serialized bytes of Vec<Entry>: https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html
  bytes entries = 2;
}

message MessageHeader {
  uint32 num_required_signatures = 1;
  uint32 num_readonly_signed_accounts = 2;
  uint32 num_readonly_unsigned_accounts = 3;
}

message CompiledInstruction {
  uint32 program_id_index = 1;
  bytes accounts = 2;
  bytes data = 3;
}

message MessageAddressTableLookup {
  bytes account_key = 1;
  bytes writable_indexes = 2;
  bytes readonly_indexes = 3;
}

message Message {
  MessageHeader header = 1;
  repeated bytes account_keys = 2;
  bytes recent_blockhash = 3;
  repeated CompiledInstruction instructions = 4;
  bool versioned = 5;
  repeated MessageAddressTableLookup address_table_lookups = 6;
}

message Transaction {
  repeated bytes signatures = 1;
  Message message = 2;
}
```

### b. Create the build.rs File

In the root of your project, create a file named `build.rs` with the following content to compile the proto file:

```rust
fn main() {
    tonic_build::compile_protos("proto/shredstream.proto")
        .expect("Failed to compile proto files");
}
```

When you build the project, Cargo will execute `build.rs` to generate the Rust modules from your proto definitions.

## 3. Implementing the gRPC Client

### a. Import Dependencies

Add dependencies to connect to the ShredStream server:

```rust
// Import the generated modules. The string here should match the package name in your proto.
pub mod shredstream {
    tonic::include_proto!("shredstream");
}

use futures::{channel::mpsc::unbounded, sink::SinkExt};
use shredstream::{
    shreder_service_client::ShrederServiceClient, SubscribeRequestFilterTransactions,
    SubscribeTransactionsRequest, SubscribeTransactionsResponse,
};
use tonic::{Response, Streaming};
```

### b. Create gRPC Client

In the `main` function, write the following code to create a gRPC connection:

```rust
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Replace with your ShredStream server address
    let endpoint = "http://127.0.0.1:9999";

    println!("Connecting to ShredStream server at: {}", endpoint);

    let mut client = ShrederServiceClient::connect(endpoint)
        .await?;

    println!("Successfully connected to server");

    // Continue with the code...
```

### c. Subscribe to Transaction Updates

Create a subscribe transaction request with the following format:
``` rust
    let request = SubscribeTransactionsRequest {
        transactions: maplit::hashmap! {
            "pumpfun".to_owned() => SubscribeRequestFilterTransactions {
                // excludes transactions involving any of these accounts
                account_exclude: vec![],
                // allows only transactions involving any of these accounts
                account_include: vec![],
                // allows only transactions involving all of these accounts
                account_required: vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_owned()]
            }
        },
    };
```
Create a stream and send incoming channel to the server. As a response you will get stream object that you can be used for obtaining new transactions:
```rust
    let (mut subscribe_tx, subscribe_rx) = unbounded();
    let response: Response<Streaming<SubscribeTransactionsResponse>> =
        client.subscribe_transactions(subscribe_rx).await.unwrap();

    let mut stream = response.into_inner();
```

Send the filter request into the outgoing channel
```rust
    let _ = subscribe_tx.send(request).await;
```

### d. Handle New Entries

With this stream, you will receive transactions based on your filter. Each transaction follows the format described in the [proto file](/proto/shredstream.proto).\
**NOTE:** A transaction may be sent multiple times. Please ensure on your end that you process each transaction only once.
```rust
   while let Some(message) = stream.message().await.unwrap() {
        println!(
            "Filters: {:?}, Sig: {:?}",
            message.filters,
            bs58::encode(&message.transaction.unwrap().transaction.unwrap().signatures[0])
                .into_string()
        )
    }

    Ok(())
}
```

## 4. Building and Running the Client

To build and run your ShredStream client:

```bash
cargo run
```

Make sure to update the endpoin in this example. You can get access by contacting Shreder team via official Discord or Website.

## 5. Further Resources

- [Solana Documentation](https://docs.solana.com/)
- [Tonic gRPC Documentation](https://github.com/hyperium/tonic)
- [Solana Entry Documentation](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html)


Shreder website: https://shreder.xyz/
Discord: https://discord.gg/5FPgveHn
X: https://x.com/ShrederXyz

Connect with us to get access to Shreder.
</file>

<file path=".gitignore">
# Generated by Cargo
# will have compiled files and executables
debug/
target/

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

# RustRover
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/.DS_Store
**/.DS_Store
</file>

<file path="build.rs">
use tonic_build::configure;

fn main() {
    const PROTOC_ENVAR: &str = "PROTOC";
    if std::env::var(PROTOC_ENVAR).is_err() {
        #[cfg(not(windows))]
        std::env::set_var(PROTOC_ENVAR, protobuf_src::protoc());
    }

    configure()
        .compile(&["proto/shredstream.proto"], &["protos"])
        .unwrap();
}
</file>

<file path="Cargo.toml">
[package]
name = "shreder-client-example"
version = "0.1.0"
edition = "2021"

[[example]]
name = "entries"
path = "src/examples/entries/main.rs"

[[example]]
name = "transactions"
path = "src/examples/transactions/main.rs"

[dependencies]
bincode = "1.3.3"
futures = "0.3.24"
solana-entry = "=2.2.1"
tokio = { version = "1", features = ["full"] }
tonic = { version = "0.10", features = ["tls", "tls-roots", "tls-webpki-roots"] }
maplit = "1.0.2"
prost = "0.12"
prost-types = "0.12"
protobuf-src = "1"
bs58 = "0.4"

[build-dependencies]
protobuf-src = "1"
tonic-build = "0.10"
</file>

<file path="LICENSE">
MIT License — Non-Commercial Use Only

Copyright (c) 2025 Shreder.xyz

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, and/or sublicense the Software,
for non-commercial purposes only, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

Commercial use is strictly prohibited without prior written permission from the copyright holder.
</file>

<file path="Readme.md">
# Shreder Rust Example

Shreder is the fastest way to get block updates and receive transaction data on Solana.
Basically, Shreder is a shred streaming service. We stream raw transaction data (shreds) directly to your app — no polling, no delays.
Shreder allows you to receive streaming data with Solana network transactions in real-time (0 block).

## Description

This repository demonstrates a basic project to test Shreder and consume transactions. It contains 2 examples with entries and transactions consuming.

## Getting Started

Follow these instructions to set up the project on your local machine.

### Prerequisites

Before you begin, make sure you have the following installed:

- [Git](https://git-scm.com/) for cloning the repository
- [Rust](https://www.rust-lang.org/tools/install) for building the project

### Usage
1. **Clone the repository**
  ```bash
   git clone https://github.com/shrederxyz/shreder-rust-example.git
   ```

2.  **Configure the project**

    In the src/examples/entries/main.rs or src/examples/transactions/main.rs file change http://localhost:9991 to url link provided by the Shreder team. Connect with us via official website or Discord.

3. **Run script**

   To run [entries](/src/examples/entries/) example
   ```bash
      cargo run --example entries
   ```
   For additional information about entries use [documentation](/src/examples/entries/Readme.md)

   To run [transactions](/src/examples/transactions/) example
   ```bash
      cargo run --example transactions
   ```
   For additional information about transactions use [documentation](/src/examples/transactions/Readme.md)

Shreder website: https://shreder.xyz/
Discord: https://discord.gg/8qEGZKPVDV
X: https://x.com/ShrederXyz
</file>

</files>
